<?php
// Test Specific DataTable Endpoints
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Test Specific DataTable Endpoints</h1>";
echo "<style>
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; font-weight: bold; }
    .code-block { background: #f5f5f5; padding: 10px; border-left: 4px solid #007cba; margin: 10px 0; }
    .result-box { background: #f9f9f9; padding: 15px; margin: 10px 0; border: 1px solid #ddd; }
</style>";

// Include the application functions
include './includes/functions.php';

echo "<h2>🔍 Environment Check</h2>";
$base_url = $db->base_url();
echo "<p><strong>Base URL:</strong> " . $base_url . "</p>";

// Check if user is logged in
$logged_in = isset($user) && is_logged_in($user);
echo "<p><strong>Logged In:</strong> " . ($logged_in ? '<span class="success">YES</span>' : '<span class="error">NO</span>') . "</p>";

if (!$logged_in) {
    echo "<div style='background: #ffebee; padding: 15px; margin: 20px 0; border-left: 4px solid #f44336;'>";
    echo "<h3>⚠️ Please Login First</h3>";
    echo "<p>You need to be logged in to test the endpoints. <a href='" . $base_url . "login'>Click here to login</a></p>";
    echo "</div>";
    exit;
}

echo "<p><strong>User ID:</strong> " . (isset($user_id_2) ? $user_id_2 : 'Not Set') . "</p>";
echo "<p><strong>User Level:</strong> " . (isset($user_level_2) ? $user_level_2 : 'Not Set') . "</p>";

// Test normal-serverside endpoint directly
echo "<h2>🧪 Testing normal-serverside Endpoint</h2>";

// Simulate DataTables POST request
$_POST = [
    'draw' => 1,
    'start' => 0,
    'length' => 10,
    'search' => ['value' => '', 'regex' => false],
    'order' => [['column' => 0, 'dir' => 'asc']],
    'columns' => [['data' => 'user_id', 'searchable' => true, 'orderable' => true]]
];
$_REQUEST = $_POST;

echo "<div class='code-block'>";
echo "<h4>Testing normal-serverside.php directly:</h4>";

ob_start();
try {
    include 'content/serverside/normal-serverside.php';
    $output = ob_get_clean();
    
    echo "<p class='success'>✓ File executed successfully</p>";
    echo "<div class='result-box'>";
    echo "<h5>Output:</h5>";
    
    // Try to decode as JSON
    $json_data = json_decode($output, true);
    if ($json_data !== null) {
        echo "<p class='success'>✓ Valid JSON response</p>";
        echo "<p><strong>Records Total:</strong> " . (isset($json_data['recordsTotal']) ? $json_data['recordsTotal'] : 'Not set') . "</p>";
        echo "<p><strong>Records Filtered:</strong> " . (isset($json_data['recordsFiltered']) ? $json_data['recordsFiltered'] : 'Not set') . "</p>";
        echo "<p><strong>Data Rows:</strong> " . (isset($json_data['data']) ? count($json_data['data']) : 'Not set') . "</p>";
        
        if (isset($json_data['data']) && count($json_data['data']) > 0) {
            echo "<details><summary>Sample Data (First Row)</summary>";
            echo "<pre>" . json_encode($json_data['data'][0], JSON_PRETTY_PRINT) . "</pre>";
            echo "</details>";
        }
    } else {
        echo "<p class='error'>✗ Invalid JSON response</p>";
        echo "<details><summary>Raw Output</summary>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
        echo "</details>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    ob_end_clean();
    echo "<p class='error'>✗ Error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Reset POST data for reseller test
$_POST = [
    'draw' => 1,
    'start' => 0,
    'length' => 10,
    'search' => ['value' => '', 'regex' => false],
    'order' => [['column' => 0, 'dir' => 'asc']],
    'columns' => [['data' => 'user_id', 'searchable' => true, 'orderable' => true]]
];
$_REQUEST = $_POST;

echo "<h2>🧪 Testing reseller-serverside Endpoint</h2>";

echo "<div class='code-block'>";
echo "<h4>Testing reseller-serverside.php directly:</h4>";

ob_start();
try {
    include 'content/serverside/reseller-serverside.php';
    $output = ob_get_clean();
    
    echo "<p class='success'>✓ File executed successfully</p>";
    echo "<div class='result-box'>";
    echo "<h5>Output:</h5>";
    
    // Try to decode as JSON
    $json_data = json_decode($output, true);
    if ($json_data !== null) {
        echo "<p class='success'>✓ Valid JSON response</p>";
        echo "<p><strong>Records Total:</strong> " . (isset($json_data['recordsTotal']) ? $json_data['recordsTotal'] : 'Not set') . "</p>";
        echo "<p><strong>Records Filtered:</strong> " . (isset($json_data['recordsFiltered']) ? $json_data['recordsFiltered'] : 'Not set') . "</p>";
        echo "<p><strong>Data Rows:</strong> " . (isset($json_data['data']) ? count($json_data['data']) : 'Not set') . "</p>";
        
        if (isset($json_data['data']) && count($json_data['data']) > 0) {
            echo "<details><summary>Sample Data (First Row)</summary>";
            echo "<pre>" . json_encode($json_data['data'][0], JSON_PRETTY_PRINT) . "</pre>";
            echo "</details>";
        }
    } else {
        echo "<p class='error'>✗ Invalid JSON response</p>";
        echo "<details><summary>Raw Output</summary>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
        echo "</details>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    ob_end_clean();
    echo "<p class='error'>✗ Error: " . $e->getMessage() . "</p>";
}
echo "</div>";

echo "<h2>📊 Database Query Test</h2>";

echo "<div class='code-block'>";
echo "<h4>Testing database queries directly:</h4>";

try {
    // Test normal users query
    if ($user_id_2 == 1 || $user_level_2 == 'superadmin') {
        $sql = "SELECT * FROM users WHERE 1=1 AND user_id!='$user_id_2' AND user_level='normal' LIMIT 5";
    } else {
        $sql = "SELECT * FROM users WHERE 1=1 AND user_id!='$user_id_2' AND upline='$user_id_2' AND user_level='normal' LIMIT 5";
    }
    
    echo "<p><strong>Normal Users Query:</strong></p>";
    echo "<pre>" . htmlspecialchars($sql) . "</pre>";
    
    $query = $db->sql_query($sql);
    if ($query) {
        $count = $db->sql_numrows($query);
        echo "<p class='success'>✓ Query executed successfully - Found " . $count . " normal users</p>";
        
        if ($count > 0) {
            echo "<details><summary>Sample User Data</summary>";
            $row = $db->sql_fetchrow($query);
            echo "<pre>";
            echo "User ID: " . $row['user_id'] . "\n";
            echo "Username: " . $row['user_name'] . "\n";
            echo "User Level: " . $row['user_level'] . "\n";
            echo "Duration: " . $row['duration'] . "\n";
            echo "Device Connected: " . $row['device_connected'] . "\n";
            echo "</pre>";
            echo "</details>";
        }
    } else {
        echo "<p class='error'>✗ Normal users query failed</p>";
    }
    
    // Test reseller query
    if ($user_id_2 == 1 || $user_level_2 == 'superadmin') {
        $sql = "SELECT * FROM users WHERE 1=1 AND user_id!='$user_id_2' AND user_level='reseller' LIMIT 5";
    } else {
        $sql = "SELECT * FROM users WHERE 1=1 AND user_id!='$user_id_2' AND upline='$user_id_2' AND user_level='reseller' LIMIT 5";
    }
    
    echo "<p><strong>Reseller Query:</strong></p>";
    echo "<pre>" . htmlspecialchars($sql) . "</pre>";
    
    $query = $db->sql_query($sql);
    if ($query) {
        $count = $db->sql_numrows($query);
        echo "<p class='success'>✓ Query executed successfully - Found " . $count . " resellers</p>";
        
        if ($count > 0) {
            echo "<details><summary>Sample Reseller Data</summary>";
            $row = $db->sql_fetchrow($query);
            echo "<pre>";
            echo "User ID: " . $row['user_id'] . "\n";
            echo "Username: " . $row['user_name'] . "\n";
            echo "User Level: " . $row['user_level'] . "\n";
            echo "Duration: " . $row['duration'] . "\n";
            echo "Device Connected: " . $row['device_connected'] . "\n";
            echo "</pre>";
            echo "</details>";
        }
    } else {
        echo "<p class='error'>✗ Reseller query failed</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>✗ Database Error: " . $e->getMessage() . "</p>";
}
echo "</div>";

echo "<h2>🔗 Next Steps</h2>";
echo "<ol>";
echo "<li>If the endpoints are working here, try the actual pages:</li>";
echo "<ul>";
echo "<li><a href='" . $base_url . "view-user' target='_blank'>User Management Page</a></li>";
echo "<li><a href='" . $base_url . "view-reseller' target='_blank'>Reseller Management Page</a></li>";
echo "</ul>";
echo "<li>If pages still don't work, check browser console (F12) for JavaScript errors</li>";
echo "<li>Check browser Network tab for failed AJAX requests</li>";
echo "<li>Clear browser cache and try again</li>";
echo "</ol>";

?>
