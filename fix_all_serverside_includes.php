<?php
// <PERSON>ript to fix all serverside files missing includes
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Fix All Serverside Includes</h1>";
echo "<style>
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; font-weight: bold; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

$serverside_dir = 'content/serverside/';
$files_to_check = glob($serverside_dir . '*-serverside.php');

echo "<h2>Checking Serverside Files</h2>";
echo "<table>";
echo "<tr><th>File</th><th>Status</th><th>Action</th></tr>";

$fixed_count = 0;
$already_fixed_count = 0;
$error_count = 0;

foreach ($files_to_check as $file_path) {
    $filename = basename($file_path);
    
    if (!file_exists($file_path)) {
        echo "<tr><td>" . $filename . "</td><td class='error'>File not found</td><td>-</td></tr>";
        $error_count++;
        continue;
    }
    
    $content = file_get_contents($file_path);
    
    // Check if it already has the proper includes
    if (strpos($content, 'require_once') !== false || strpos($content, 'include') !== false) {
        echo "<tr><td>" . $filename . "</td><td class='success'>Already has includes</td><td>No action needed</td></tr>";
        $already_fixed_count++;
        continue;
    }
    
    // Check if it starts with the pattern we need to fix
    if (preg_match('/^<\?php\s*\n\s*chkSession\(\);/', $content)) {
        // Fix the file
        $new_content = preg_replace(
            '/^(<\?php)\s*\n\s*(chkSession\(\);)/',
            '$1' . "\n" . 
            'error_reporting(E_ERROR | E_PARSE);' . "\n" .
            'ini_set(\'display_errors\', \'1\');' . "\n" .
            'require_once \'../../includes/functions.php\';' . "\n\n" .
            '$2',
            $content
        );
        
        if ($new_content !== $content) {
            if (file_put_contents($file_path, $new_content)) {
                echo "<tr><td>" . $filename . "</td><td class='success'>Fixed</td><td>Added includes</td></tr>";
                $fixed_count++;
            } else {
                echo "<tr><td>" . $filename . "</td><td class='error'>Failed to write</td><td>Permission error</td></tr>";
                $error_count++;
            }
        } else {
            echo "<tr><td>" . $filename . "</td><td class='warning'>Pattern not matched</td><td>Manual check needed</td></tr>";
            $error_count++;
        }
    } else {
        echo "<tr><td>" . $filename . "</td><td class='warning'>Different pattern</td><td>Manual check needed</td></tr>";
        $error_count++;
    }
}

echo "</table>";

echo "<h2>Summary</h2>";
echo "<table>";
echo "<tr><th>Status</th><th>Count</th></tr>";
echo "<tr><td>Files Fixed</td><td class='success'>" . $fixed_count . "</td></tr>";
echo "<tr><td>Already Fixed</td><td class='info'>" . $already_fixed_count . "</td></tr>";
echo "<tr><td>Errors/Manual Check</td><td class='" . ($error_count > 0 ? 'warning' : 'success') . "'>" . $error_count . "</td></tr>";
echo "<tr><td>Total Files</td><td>" . count($files_to_check) . "</td></tr>";
echo "</table>";

if ($fixed_count > 0) {
    echo "<div style='background: #e8f5e8; padding: 15px; margin: 10px 0; border-left: 4px solid #4caf50;'>";
    echo "<h3>✅ Success!</h3>";
    echo "<p>Fixed " . $fixed_count . " serverside files. You should now test the DataTables functionality.</p>";
    echo "</div>";
}

if ($error_count > 0) {
    echo "<div style='background: #fff3cd; padding: 15px; margin: 10px 0; border-left: 4px solid #ffc107;'>";
    echo "<h3>⚠️ Manual Check Required</h3>";
    echo "<p>" . $error_count . " files need manual checking. Please review them individually.</p>";
    echo "</div>";
}

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li>Clear template cache: <code>rm templates_c/*.php</code></li>";
echo "<li>Test the DataTables: <a href='diagnose_datatable_issues.php'>Run Diagnostic</a></li>";
echo "<li>Check user list page: <a href='view-user'>View Users</a></li>";
echo "<li>Check reseller page: <a href='view-reseller'>View Resellers</a></li>";
echo "</ol>";

?>
