<?php
// AJAX Issues Fix Script
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>AJAX Issues Fix</h1>";

// Include functions
include './includes/functions.php';

echo "<h2>Diagnosis Results</h2>";

// Check 1: Session Status
echo "<h3>1. Session Status</h3>";
if (isset($user) && is_logged_in($user)) {
    echo "<p style='color: green;'>✓ User is logged in</p>";
    echo "<p>User ID: " . (isset($user_id_2) ? $user_id_2 : 'Not set') . "</p>";
    echo "<p>User Level: " . (isset($user_level_2) ? $user_level_2 : 'Not set') . "</p>";
    
    // Check permissions
    if (isset($user_id_2) && isset($user_level_2)) {
        if ($user_id_2 == 1 || $user_level_2 == 'superadmin' || $user_level_2 == 'reseller') {
            echo "<p style='color: green;'>✓ User has permission to access serverside endpoints</p>";
        } else {
            echo "<p style='color: red;'>✗ User doesn't have permission to access serverside endpoints</p>";
            echo "<p><strong>Issue:</strong> You need to be superadmin, reseller, or user_id=1</p>";
        }
    }
} else {
    echo "<p style='color: red;'>✗ User is not logged in</p>";
    echo "<p><strong>Solution:</strong> <a href='" . $db->base_url() . "login'>Please login first</a></p>";
}

// Check 2: Base URL Configuration
echo "<h3>2. Base URL Configuration</h3>";
$base_url = $db->base_url();
echo "<p><strong>Current Base URL:</strong> " . $base_url . "</p>";

if (strpos($base_url, '/RajaGenWeb/') !== false) {
    echo "<p style='color: green;'>✓ Base URL includes /RajaGenWeb/ subdirectory</p>";
} else {
    echo "<p style='color: red;'>✗ Base URL missing /RajaGenWeb/ subdirectory</p>";
}

// Check 3: Serverside Files
echo "<h3>3. Serverside Files</h3>";
$endpoints = ['normal-serverside', 'bulk-serverside', 'inactive-serverside', 'trial-serverside'];
foreach ($endpoints as $endpoint) {
    $file_path = "content/serverside/" . $endpoint . ".php";
    if (file_exists($file_path)) {
        echo "<p style='color: green;'>✓ " . $endpoint . ".php exists</p>";
    } else {
        echo "<p style='color: red;'>✗ " . $endpoint . ".php missing</p>";
    }
}

// Check 4: Template Cache
echo "<h3>4. Template Cache</h3>";
$template_file = "templates_c/4518f2c26026955e3e338431240c83486a53bee9_0.file.viewuser_js.tpl.php";
if (file_exists($template_file)) {
    echo "<p style='color: green;'>✓ Compiled template exists</p>";
    
    // Check if base_url is properly used in template
    $template_content = file_get_contents($template_file);
    if (strpos($template_content, 'base_url') !== false) {
        echo "<p style='color: green;'>✓ Template uses base_url variable</p>";
    } else {
        echo "<p style='color: red;'>✗ Template doesn't use base_url variable</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Compiled template missing</p>";
}

// Check 5: .htaccess Configuration
echo "<h3>5. .htaccess Configuration</h3>";
if (file_exists('.htaccess')) {
    $htaccess_content = file_get_contents('.htaccess');
    if (strpos($htaccess_content, 'RewriteBase /RajaGenWeb/') !== false) {
        echo "<p style='color: green;'>✓ .htaccess has correct RewriteBase</p>";
    } else {
        echo "<p style='color: red;'>✗ .htaccess missing correct RewriteBase</p>";
    }
    
    if (strpos($htaccess_content, 'RewriteRule ^([^/.]+)/?$ index.php?p=$1') !== false) {
        echo "<p style='color: green;'>✓ .htaccess has correct rewrite rule</p>";
    } else {
        echo "<p style='color: red;'>✗ .htaccess missing correct rewrite rule</p>";
    }
} else {
    echo "<p style='color: red;'>✗ .htaccess file missing</p>";
}

echo "<h2>Recommended Solutions</h2>";

echo "<h3>Solution 1: Clear Template Cache</h3>";
echo "<p>Clear the Smarty template cache to ensure changes take effect:</p>";
echo "<code>rm -f templates_c/*.php</code>";

echo "<h3>Solution 2: Check Login Status</h3>";
echo "<p>Make sure you're logged in with proper permissions:</p>";
echo "<ul>";
echo "<li>Login as superadmin or reseller</li>";
echo "<li>Or login as user_id = 1</li>";
echo "</ul>";

echo "<h3>Solution 3: Test Individual Endpoints</h3>";
echo "<p>Test each endpoint manually:</p>";
echo "<ul>";
foreach ($endpoints as $endpoint) {
    echo "<li><a href='" . $base_url . $endpoint . "' target='_blank'>" . $endpoint . "</a></li>";
}
echo "</ul>";

echo "<h3>Solution 4: Browser Console Check</h3>";
echo "<p>Open browser developer tools and check:</p>";
echo "<ul>";
echo "<li>Network tab for failed requests</li>";
echo "<li>Console tab for JavaScript errors</li>";
echo "<li>Check if AJAX requests are being made to correct URLs</li>";
echo "</ul>";

echo "<h2>Quick Fix Actions</h2>";

// Action 1: Clear template cache
if (isset($_GET['action']) && $_GET['action'] == 'clear_cache') {
    $cache_files = glob('templates_c/*.php');
    $deleted = 0;
    foreach ($cache_files as $file) {
        if (unlink($file)) {
            $deleted++;
        }
    }
    echo "<p style='color: green;'>✓ Cleared " . $deleted . " template cache files</p>";
    echo "<p><a href='?'>Refresh this page</a></p>";
}

if (!isset($_GET['action'])) {
    echo "<p><a href='?action=clear_cache' class='btn btn-primary'>Clear Template Cache</a></p>";
}

echo "<h2>Test Pages</h2>";
echo "<ul>";
echo "<li><a href='" . $base_url . "view-user' target='_blank'>View User Page</a></li>";
echo "<li><a href='" . $base_url . "view-reseller' target='_blank'>View Reseller Page</a></li>";
echo "<li><a href='" . $base_url . "json' target='_blank'>JSON Page</a></li>";
echo "<li><a href='" . $base_url . "notification' target='_blank'>Notification Page</a></li>";
echo "</ul>";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
p { margin: 10px 0; }
code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
.btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
.btn:hover { background: #0056b3; }
</style>";
?>
