<?php
// Comprehensive AJAX Diagnostic Script
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>AJAX Diagnostic Report</h1>";
echo "<style>
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .test-button { background: #007cba; color: white; padding: 5px 10px; border: none; cursor: pointer; }
</style>";

// Include the application functions
include './includes/functions.php';

echo "<h2>1. Environment Check</h2>";
echo "<table>";
echo "<tr><th>Check</th><th>Status</th><th>Value</th></tr>";

// Check PHP version
echo "<tr><td>PHP Version</td><td class='success'>✓</td><td>" . PHP_VERSION . "</td></tr>";

// Check if session is started
echo "<tr><td>Session Started</td><td class='" . (session_status() === PHP_SESSION_ACTIVE ? 'success' : 'error') . "'>" . 
     (session_status() === PHP_SESSION_ACTIVE ? '✓' : '✗') . "</td><td>" . 
     (session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Not Active') . "</td></tr>";

// Check base URL
$base_url = $db->base_url();
echo "<tr><td>Base URL</td><td class='" . (!empty($base_url) ? 'success' : 'error') . "'>" . 
     (!empty($base_url) ? '✓' : '✗') . "</td><td>" . $base_url . "</td></tr>";

// Check if user is logged in
$logged_in = isset($user) && is_logged_in($user);
echo "<tr><td>User Logged In</td><td class='" . ($logged_in ? 'success' : 'error') . "'>" . 
     ($logged_in ? '✓' : '✗') . "</td><td>" . ($logged_in ? 'Yes' : 'No') . "</td></tr>";

if ($logged_in) {
    echo "<tr><td>User ID</td><td class='success'>✓</td><td>" . (isset($user_id_2) ? $user_id_2 : 'Not Set') . "</td></tr>";
    echo "<tr><td>User Level</td><td class='success'>✓</td><td>" . (isset($user_level_2) ? $user_level_2 : 'Not Set') . "</td></tr>";
    
    // Check permissions
    $has_permission = ($user_id_2 == 1 || $user_level_2 == 'superadmin' || $user_level_2 == 'reseller' || $user_level_2 == 'developer');
    echo "<tr><td>Has AJAX Permissions</td><td class='" . ($has_permission ? 'success' : 'error') . "'>" . 
         ($has_permission ? '✓' : '✗') . "</td><td>" . ($has_permission ? 'Yes' : 'No') . "</td></tr>";
}

echo "</table>";

echo "<h2>2. AJAX Endpoints Check</h2>";

// List of AJAX endpoints to test
$ajax_endpoints = [
    'get_userinfo' => 'serverside/data/get_userinfo.php',
    'get_useroption' => 'serverside/data/get_useroption.php', 
    'get_active' => 'serverside/data/get_active.php',
    'normal-serverside' => 'content/serverside/normal-serverside.php',
    'bulk-serverside' => 'content/serverside/bulk-serverside.php',
    'inactive-serverside' => 'content/serverside/inactive-serverside.php',
    'trial-serverside' => 'content/serverside/trial-serverside.php'
];

echo "<table>";
echo "<tr><th>Endpoint</th><th>File Exists</th><th>Full URL</th><th>Test</th></tr>";

foreach ($ajax_endpoints as $endpoint => $file_path) {
    $file_exists = file_exists($file_path);
    $full_url = $base_url . $endpoint;
    
    echo "<tr>";
    echo "<td>" . $endpoint . "</td>";
    echo "<td class='" . ($file_exists ? 'success' : 'error') . "'>" . 
         ($file_exists ? '✓ Yes' : '✗ No') . "</td>";
    echo "<td><a href='" . $full_url . "' target='_blank'>" . $full_url . "</a></td>";
    echo "<td><button class='test-button' onclick='testAjaxEndpoint(\"" . $endpoint . "\")'>Test</button> " .
         "<span id='result_" . str_replace('-', '_', $endpoint) . "'></span></td>";
    echo "</tr>";
}

echo "</table>";

echo "<h2>3. URL Routing Check</h2>";
echo "<table>";
echo "<tr><th>Check</th><th>Status</th><th>Details</th></tr>";

// Check .htaccess
$htaccess_exists = file_exists('.htaccess');
echo "<tr><td>.htaccess File</td><td class='" . ($htaccess_exists ? 'success' : 'error') . "'>" . 
     ($htaccess_exists ? '✓' : '✗') . "</td><td>" . ($htaccess_exists ? 'Exists' : 'Missing') . "</td></tr>";

if ($htaccess_exists) {
    $htaccess_content = file_get_contents('.htaccess');
    $has_rewrite_base = strpos($htaccess_content, 'RewriteBase /RajaGenWeb/') !== false;
    echo "<tr><td>RewriteBase</td><td class='" . ($has_rewrite_base ? 'success' : 'warning') . "'>" . 
         ($has_rewrite_base ? '✓' : '?') . "</td><td>" . ($has_rewrite_base ? 'Correct' : 'Check manually') . "</td></tr>";
}

// Check index.php routing
$index_exists = file_exists('index.php');
echo "<tr><td>index.php</td><td class='" . ($index_exists ? 'success' : 'error') . "'>" . 
     ($index_exists ? '✓' : '✗') . "</td><td>" . ($index_exists ? 'Exists' : 'Missing') . "</td></tr>";

echo "</table>";

echo "<h2>4. Database Connection</h2>";
echo "<table>";
echo "<tr><th>Check</th><th>Status</th><th>Details</th></tr>";

// Test database connection
try {
    $test_query = $db->sql_query("SELECT 1 as test");
    if ($test_query) {
        echo "<tr><td>Database Connection</td><td class='success'>✓</td><td>Connected</td></tr>";
        
        // Test users table
        $users_query = $db->sql_query("SELECT COUNT(*) as count FROM users");
        if ($users_query) {
            $users_row = $db->sql_fetchrow($users_query);
            echo "<tr><td>Users Table</td><td class='success'>✓</td><td>" . $users_row['count'] . " users</td></tr>";
        }
    }
} catch (Exception $e) {
    echo "<tr><td>Database Connection</td><td class='error'>✗</td><td>Error: " . $e->getMessage() . "</td></tr>";
}

echo "</table>";

if (!$logged_in) {
    echo "<div style='background: #ffebee; padding: 15px; margin: 20px 0; border-left: 4px solid #f44336;'>";
    echo "<h3>⚠️ Authentication Required</h3>";
    echo "<p>You need to be logged in to test the AJAX endpoints. Please <a href='" . $base_url . "login'>login</a> first.</p>";
    echo "</div>";
}

?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function testAjaxEndpoint(endpoint) {
    const resultId = 'result_' + endpoint.replace('-', '_');
    const resultSpan = document.getElementById(resultId);
    resultSpan.innerHTML = '<span style="color: blue;">Testing...</span>';
    
    let ajaxData = {};
    let ajaxType = 'GET';
    
    // Configure request based on endpoint type
    if (endpoint.includes('serverside')) {
        ajaxType = 'POST';
        ajaxData = {
            draw: 1,
            start: 0,
            length: 10,
            search: {value: ''},
            order: [{column: 0, dir: 'asc'}]
        };
    } else if (endpoint.startsWith('get_')) {
        if (endpoint === 'get_userinfo' || endpoint === 'get_useroption') {
            ajaxData = {uid: 1}; // Test with user ID 1
        } else if (endpoint === 'get_active') {
            ajaxData = {type: 'normal'};
        }
    }
    
    $.ajax({
        url: '<?php echo $base_url; ?>' + endpoint,
        type: ajaxType,
        data: ajaxData,
        dataType: 'json',
        timeout: 10000,
        success: function(data) {
            console.log('Success for ' + endpoint + ':', data);
            if (data && typeof data === 'object') {
                if (data.error) {
                    resultSpan.innerHTML = '<span style="color: red;">Error: ' + data.error + '</span>';
                } else if (data.response !== undefined || data.data !== undefined || data.recordsTotal !== undefined) {
                    resultSpan.innerHTML = '<span style="color: green;">✓ Success</span>';
                } else {
                    resultSpan.innerHTML = '<span style="color: orange;">? Unexpected response</span>';
                }
            } else {
                resultSpan.innerHTML = '<span style="color: orange;">? Non-JSON response</span>';
            }
        },
        error: function(xhr, status, error) {
            console.error('Error for ' + endpoint + ':', xhr, status, error);
            let errorMsg = 'HTTP ' + xhr.status;
            if (xhr.responseText) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.error) errorMsg += ': ' + response.error;
                } catch (e) {
                    if (xhr.responseText.length < 100) {
                        errorMsg += ': ' + xhr.responseText;
                    }
                }
            }
            resultSpan.innerHTML = '<span style="color: red;">✗ ' + errorMsg + '</span>';
        }
    });
}
</script>
