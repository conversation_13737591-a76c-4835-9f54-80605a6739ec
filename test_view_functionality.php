<?php
// Test View Functionality Script
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>View Functionality Test</h1>";

// Include functions
include './includes/functions.php';

echo "<h2>Authentication Status</h2>";
if (isset($user) && is_logged_in($user)) {
    echo "<p style='color: green;'>✓ User is logged in</p>";
    echo "<p>User ID: " . (isset($user_id_2) ? $user_id_2 : 'Not set') . "</p>";
    echo "<p>User Level: " . (isset($user_level_2) ? $user_level_2 : 'Not set') . "</p>";
    echo "<p>User Name: " . (isset($user_name_2) ? $user_name_2 : 'Not set') . "</p>";
    
    // Check permissions
    if (isset($user_id_2) && isset($user_level_2)) {
        if ($user_id_2 == 1 || $user_level_2 == 'superadmin' || $user_level_2 == 'reseller' || $user_level_2 == 'developer') {
            echo "<p style='color: green;'>✓ User has permission to access view endpoints</p>";
        } else {
            echo "<p style='color: red;'>✗ User doesn't have permission to access view endpoints</p>";
            echo "<p><strong>Required:</strong> user_id=1 OR user_level in ['superadmin', 'reseller', 'developer']</p>";
        }
    }
} else {
    echo "<p style='color: red;'>✗ User is not logged in</p>";
    echo "<p><strong>Solution:</strong> <a href='" . $db->base_url() . "login'>Please login first</a></p>";
}

echo "<h2>Endpoint Accessibility Test</h2>";

$base_url = $db->base_url();
$endpoints = [
    'get_userinfo' => 'User Information',
    'get_useroption' => 'User Options',
    'get_reselleroption' => 'Reseller Options',
    'get_active' => 'Active Users Count',
    'get_data' => 'License Data',
    'get_server' => 'Server Information',
    'get_json' => 'JSON Configuration'
];

foreach ($endpoints as $endpoint => $description) {
    $file_path = "content/serverside/" . $endpoint . ".php";
    $url = $base_url . $endpoint;
    
    echo "<h3>" . $description . " (" . $endpoint . ")</h3>";
    
    // Check if routing file exists
    if (file_exists($file_path)) {
        echo "<p style='color: green;'>✓ Routing file exists: " . $file_path . "</p>";
    } else {
        echo "<p style='color: red;'>✗ Routing file missing: " . $file_path . "</p>";
    }
    
    // Check if actual data file exists
    $data_file = "serverside/data/" . $endpoint . ".php";
    if (file_exists($data_file)) {
        echo "<p style='color: green;'>✓ Data file exists: " . $data_file . "</p>";
    } else {
        echo "<p style='color: red;'>✗ Data file missing: " . $data_file . "</p>";
    }
    
    echo "<p><strong>URL:</strong> <a href='" . $url . "' target='_blank'>" . $url . "</a></p>";
}

echo "<h2>Test Sample User Data</h2>";

// Get a sample user for testing
$sql = "SELECT user_id, user_name FROM users WHERE user_id != '$user_id_2' LIMIT 1";
$qry = $db->sql_query($sql);
$sample_user = $db->sql_fetchrow($qry);

if ($sample_user) {
    $sample_uid = $sample_user['user_id'];
    $sample_username = $sample_user['user_name'];
    
    echo "<p><strong>Sample User:</strong> " . $sample_username . " (ID: " . $sample_uid . ")</p>";
    
    // Test get_userinfo endpoint
    echo "<h3>Test get_userinfo Endpoint</h3>";
    $test_url = $base_url . "get_userinfo?uid=" . $sample_uid;
    echo "<p><strong>Test URL:</strong> <a href='" . $test_url . "' target='_blank'>" . $test_url . "</a></p>";
    
    // Test get_useroption endpoint
    echo "<h3>Test get_useroption Endpoint</h3>";
    $test_url2 = $base_url . "get_useroption?uid=" . $sample_uid;
    echo "<p><strong>Test URL:</strong> <a href='" . $test_url2 . "' target='_blank'>" . $test_url2 . "</a></p>";
    
    // Test get_active endpoint
    echo "<h3>Test get_active Endpoint</h3>";
    $test_url3 = $base_url . "get_active?type=normal";
    echo "<p><strong>Test URL:</strong> <a href='" . $test_url3 . "' target='_blank'>" . $test_url3 . "</a></p>";
    
} else {
    echo "<p style='color: orange;'>⚠ No sample users found for testing</p>";
}

echo "<h2>JavaScript Test</h2>";
echo "<p>Test the view_info and user_option functions:</p>";

if ($sample_user) {
    echo "<script>
    function testViewInfo() {
        if (typeof view_info === 'function') {
            view_info(" . $sample_uid . ");
        } else {
            alert('view_info function not found. Make sure you are on the view-user page.');
        }
    }
    
    function testUserOption() {
        if (typeof user_option === 'function') {
            user_option(" . $sample_uid . ");
        } else {
            alert('user_option function not found. Make sure you are on the view-user page.');
        }
    }
    </script>";
    
    echo "<button onclick='testViewInfo()' style='margin: 5px; padding: 10px; background: #007bff; color: white; border: none; border-radius: 5px;'>Test View Info</button>";
    echo "<button onclick='testUserOption()' style='margin: 5px; padding: 10px; background: #28a745; color: white; border: none; border-radius: 5px;'>Test User Option</button>";
}

echo "<h2>Page Links</h2>";
echo "<ul>";
echo "<li><a href='" . $base_url . "view-user' target='_blank'>View User Page</a></li>";
echo "<li><a href='" . $base_url . "view-reseller' target='_blank'>View Reseller Page</a></li>";
echo "<li><a href='" . $base_url . "dashboard' target='_blank'>Dashboard</a></li>";
echo "</ul>";

echo "<h2>Browser Console Instructions</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>To test in browser console:</strong></p>";
echo "<ol>";
echo "<li>Go to the <a href='" . $base_url . "view-user' target='_blank'>View User page</a></li>";
echo "<li>Open browser developer tools (F12)</li>";
echo "<li>Go to Console tab</li>";
echo "<li>Click on a view icon (eye icon) in the user table</li>";
echo "<li>Check for any error messages in the console</li>";
echo "<li>Check Network tab for failed AJAX requests</li>";
echo "</ol>";
echo "</div>";

echo "<h2>Expected Results</h2>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>After the fix, you should see:</strong></p>";
echo "<ul>";
echo "<li>✅ No 'Failed getting table data from ajax' errors</li>";
echo "<li>✅ View icons open modal dialogs with user information</li>";
echo "<li>✅ AJAX requests go to URLs like: " . $base_url . "get_userinfo</li>";
echo "<li>✅ No 404 errors in browser console</li>";
echo "<li>✅ JSON responses with user data</li>";
echo "</ul>";
echo "</div>";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
p { margin: 10px 0; }
code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
</style>";
?>
