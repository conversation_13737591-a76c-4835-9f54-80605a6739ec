<?php
// Test DataTable Endpoints
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>DataTable Endpoints Test</h1>";
echo "<style>
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; font-weight: bold; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .test-button { background: #007cba; color: white; padding: 5px 10px; border: none; cursor: pointer; margin: 2px; }
    .test-all { background: #28a745; color: white; padding: 10px 20px; border: none; cursor: pointer; margin: 10px 0; }
    .code-block { background: #f5f5f5; padding: 10px; border-left: 4px solid #007cba; margin: 10px 0; }
</style>";

// Include the application functions
include './includes/functions.php';

echo "<h2>🔍 Current Status</h2>";
$base_url = $db->base_url();
echo "<p><strong>Base URL:</strong> " . $base_url . "</p>";

// Check if user is logged in
$logged_in = isset($user) && is_logged_in($user);
echo "<p><strong>Logged In:</strong> " . ($logged_in ? '<span class="success">YES</span>' : '<span class="error">NO</span>') . "</p>";

if ($logged_in) {
    echo "<p><strong>User ID:</strong> " . (isset($user_id_2) ? $user_id_2 : 'Not Set') . "</p>";
    echo "<p><strong>User Level:</strong> " . (isset($user_level_2) ? $user_level_2 : 'Not Set') . "</p>";
    
    $has_permission = ($user_id_2 == 1 || $user_level_2 == 'superadmin' || $user_level_2 == 'reseller' || $user_level_2 == 'developer');
    echo "<p><strong>Has Permissions:</strong> " . ($has_permission ? '<span class="success">YES</span>' : '<span class="error">NO</span>') . "</p>";
}

if (!$logged_in) {
    echo "<div style='background: #ffebee; padding: 15px; margin: 20px 0; border-left: 4px solid #f44336;'>";
    echo "<h3>⚠️ Please Login First</h3>";
    echo "<p>You need to be logged in to test the DataTable endpoints. <a href='" . $base_url . "login'>Click here to login</a></p>";
    echo "</div>";
    exit;
}

echo "<h2>📊 Database Quick Check</h2>";
try {
    $users_query = $db->sql_query("SELECT COUNT(*) as count FROM users");
    if ($users_query) {
        $users_row = $db->sql_fetchrow($users_query);
        echo "<p><strong>Total Users in Database:</strong> " . $users_row['count'] . "</p>";
        
        // Check different user types
        $normal_query = $db->sql_query("SELECT COUNT(*) as count FROM users WHERE user_level='normal'");
        $normal_row = $db->sql_fetchrow($normal_query);
        echo "<p><strong>Normal Users:</strong> " . $normal_row['count'] . "</p>";
        
        $reseller_query = $db->sql_query("SELECT COUNT(*) as count FROM users WHERE user_level='reseller'");
        $reseller_row = $db->sql_fetchrow($reseller_query);
        echo "<p><strong>Resellers:</strong> " . $reseller_row['count'] . "</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>Database Error: " . $e->getMessage() . "</p>";
}

echo "<h2>🧪 DataTable Endpoints Test</h2>";
echo "<button class='test-all' onclick='testAllDataTableEndpoints()'>Test All DataTable Endpoints</button>";
echo "<div id='overall-status'></div>";

$endpoints = [
    'normal-serverside' => 'User List (Normal)',
    'reseller-serverside' => 'Reseller List', 
    'bulk-serverside' => 'Bulk Users',
    'inactive-serverside' => 'Inactive Users',
    'trial-serverside' => 'Trial Users',
    'json-serverside' => 'JSON Data',
    'application-serverside' => 'Applications',
    'server-serverside' => 'Servers',
    'notification-serverside' => 'Notifications'
];

echo "<table>";
echo "<tr><th>Endpoint</th><th>Description</th><th>Status</th><th>Action</th></tr>";

foreach ($endpoints as $endpoint => $description) {
    echo "<tr>";
    echo "<td>" . $endpoint . "</td>";
    echo "<td>" . $description . "</td>";
    echo "<td><span id='status_" . str_replace('-', '_', $endpoint) . "'>Not Tested</span></td>";
    echo "<td><button class='test-button' onclick='testDataTableEndpoint(\"" . $endpoint . "\")'>Test</button></td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>Test Results</h3>";
echo "<div id='test-results'></div>";

echo "<h2>🔗 Direct Page Links</h2>";
echo "<ul>";
echo "<li><a href='" . $base_url . "view-user' target='_blank'>User Management Page</a></li>";
echo "<li><a href='" . $base_url . "view-reseller' target='_blank'>Reseller Management Page</a></li>";
echo "</ul>";

echo "<h2>🔧 Manual Testing</h2>";
echo "<div class='code-block'>";
echo "<h4>To manually test:</h4>";
echo "<ol>";
echo "<li>Open the <a href='" . $base_url . "view-user' target='_blank'>User Management page</a></li>";
echo "<li>Check if the user table loads with data</li>";
echo "<li>Try switching between different user types (Normal, Bulk, Trial, Inactive)</li>";
echo "<li>Open browser Developer Tools (F12) and check Console and Network tabs for errors</li>";
echo "</ol>";
echo "</div>";

?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
let testResults = {};

function testDataTableEndpoint(endpoint) {
    const statusId = 'status_' + endpoint.replace('-', '_');
    const statusSpan = document.getElementById(statusId);
    statusSpan.innerHTML = '<span style="color: blue;">Testing...</span>';
    
    // Standard DataTables POST request format
    const postData = {
        draw: 1,
        start: 0,
        length: 10,
        search: {
            value: '',
            regex: false
        },
        order: [
            {
                column: 0,
                dir: 'asc'
            }
        ],
        columns: [
            {
                data: 'user_id',
                name: '',
                searchable: true,
                orderable: true,
                search: {
                    value: '',
                    regex: false
                }
            }
        ]
    };
    
    $.ajax({
        url: '<?php echo $base_url; ?>' + endpoint,
        type: 'POST',
        data: postData,
        dataType: 'json',
        timeout: 15000,
        success: function(data) {
            console.log('Success for ' + endpoint + ':', data);
            testResults[endpoint] = {success: true, data: data};
            
            if (data && typeof data === 'object') {
                if (data.error) {
                    statusSpan.innerHTML = '<span style="color: red;">Error: ' + data.error + '</span>';
                } else if (data.data !== undefined && data.recordsTotal !== undefined) {
                    statusSpan.innerHTML = '<span style="color: green;">✓ Success (' + data.recordsTotal + ' records)</span>';
                } else {
                    statusSpan.innerHTML = '<span style="color: orange;">? Unexpected response format</span>';
                }
            } else {
                statusSpan.innerHTML = '<span style="color: orange;">? Non-JSON response</span>';
            }
            
            updateTestResults();
        },
        error: function(xhr, status, error) {
            console.error('Error for ' + endpoint + ':', xhr, status, error);
            testResults[endpoint] = {success: false, error: xhr.status + ': ' + error, response: xhr.responseText};
            
            let errorMsg = 'HTTP ' + xhr.status;
            if (xhr.responseText && xhr.responseText.length < 200) {
                errorMsg += ': ' + xhr.responseText.substring(0, 100);
            }
            statusSpan.innerHTML = '<span style="color: red;">✗ ' + errorMsg + '</span>';
            
            updateTestResults();
        }
    });
}

function testAllDataTableEndpoints() {
    const endpoints = Object.keys(<?php echo json_encode($endpoints); ?>);
    
    testResults = {};
    document.getElementById('overall-status').innerHTML = '<p style="color: blue;">Testing all DataTable endpoints...</p>';
    
    endpoints.forEach((endpoint, index) => {
        setTimeout(() => {
            testDataTableEndpoint(endpoint);
        }, index * 1000); // Stagger tests by 1 second
    });
    
    // Check overall status after all tests
    setTimeout(() => {
        checkOverallStatus();
    }, endpoints.length * 1000 + 2000);
}

function checkOverallStatus() {
    const totalTests = Object.keys(testResults).length;
    const successfulTests = Object.values(testResults).filter(result => result.success).length;
    
    const overallDiv = document.getElementById('overall-status');
    if (successfulTests === totalTests && totalTests > 0) {
        overallDiv.innerHTML = '<p style="color: green; font-weight: bold;">🎉 All ' + totalTests + ' DataTable endpoints are working correctly!</p>';
    } else if (successfulTests > 0) {
        overallDiv.innerHTML = '<p style="color: orange; font-weight: bold;">⚠️ ' + successfulTests + ' out of ' + totalTests + ' endpoints are working.</p>';
    } else {
        overallDiv.innerHTML = '<p style="color: red; font-weight: bold;">❌ No endpoints are working. Check the detailed results below.</p>';
    }
}

function updateTestResults() {
    const resultsDiv = document.getElementById('test-results');
    let html = '';
    
    for (const [endpoint, result] of Object.entries(testResults)) {
        html += '<div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd;">';
        html += '<h4>' + endpoint + '</h4>';
        
        if (result.success) {
            html += '<p style="color: green; font-weight: bold;">✓ Success</p>';
            if (result.data) {
                html += '<p><strong>Records Total:</strong> ' + (result.data.recordsTotal || 0) + '</p>';
                html += '<p><strong>Records Filtered:</strong> ' + (result.data.recordsFiltered || 0) + '</p>';
                html += '<p><strong>Data Rows:</strong> ' + (result.data.data ? result.data.data.length : 0) + '</p>';
                
                if (result.data.data && result.data.data.length > 0) {
                    html += '<details><summary>Sample Data (First Row)</summary><pre>' + JSON.stringify(result.data.data[0], null, 2) + '</pre></details>';
                }
            }
        } else {
            html += '<p style="color: red; font-weight: bold;">✗ Failed: ' + result.error + '</p>';
            if (result.response && result.response.length < 1000) {
                html += '<details><summary>Error Response</summary><pre>' + result.response + '</pre></details>';
            }
        }
        
        html += '</div>';
    }
    
    resultsDiv.innerHTML = html;
}
</script>
