<?php
/* Smarty version 3.1.29, created on 2025-07-14 00:21:46
  from "C:\xampp\htdocs\RajaGenWeb\templates\js\page\logdelete_js.tpl" */

if ($_smarty_tpl->smarty->ext->_validateCompiled->decodeProperties($_smarty_tpl, array (
  'has_nocache_code' => false,
  'version' => '3.1.29',
  'unifunc' => 'content_6874236a80ac75_11527627',
  'file_dependency' => 
  array (
    '1b533007aa960d7228980bad961f31959b170ecb' => 
    array (
      0 => 'C:\\xampp\\htdocs\\RajaGenWeb\\templates\\js\\page\\logdelete_js.tpl',
      1 => 1752437712,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_6874236a80ac75_11527627 ($_smarty_tpl) {
echo '<script'; ?>
>
$('document').ready(function()
{
    $.fn.dataTable.ext.errMode = () => swal(`Failed`, `Failed getting data from AJAX.`, `warning`, {
        button: false,
        closeOnClickOutside: false,
        timer: 3000
    }).then(() => {
        location.reload()
    });
	table = $('.table-deleted').dataTable({
        "bProcessing": false,
        "bServerSide": true,
        "ajax": {
            "url": "<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
log-deleted-serverside",
            "type": "POST"
        },
		order: [[0, 'desc']],
        responsive: true,
        "language": {                
                "infoFiltered": ""
            },
        columnDefs: [
          {
            width: '20%',
            targets: 0,
          },
          {
            width: '20%',
            targets: 1,
          },
          {
            width: '20%',
            targets: 2,
          },
          {
            width: '20%',
            targets: 3,
          },
        ],
	});
	function getD(){
        $.ajax({
            url: "<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
serverside/data/get_data.php",
            type: "GET",
            dataType: "JSON",
        	cache: false,
            success: function(data)
            {
        		if(data.response == 1){
       
                }
                if(data.response == 2){
                	swal(`Error`, data.licmsg, `error`, {
                        button: false,
                        closeOnClickOutside: false,
                        timer: 5000
                    }).then(() => {
                        location.reload()
                    });
                }
                if(data.response == 3){
                	swal(`Error`, data.licmsg, `error`, {
                        button: false,
                        closeOnClickOutside: false,
                        timer: 5000
                    }).then(() => {
                        location.reload()
                    });
                }
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                swal(`Error`, `Error parsing data.`, `error`, {
                    button: false,
                    closeOnClickOutside: false,
                    timer: 3000
                }).then(() => {
                    location.reload()
                });
            },
            complete: function(){
        
        	}
        });
    }
    getD()
});
<?php echo '</script'; ?>
><?php }
}
