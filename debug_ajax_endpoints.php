<?php
// Debug AJAX Endpoints Script
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>AJAX Endpoints Debug</h1>";

// Include the functions to get session and base URL
include './includes/functions.php';

echo "<h2>Session Information</h2>";
echo "<p><strong>User Cookie:</strong> " . (isset($_COOKIE['user']) ? 'Set' : 'Not Set') . "</p>";

if (isset($user) && !empty($user)) {
    echo "<p><strong>User Variable:</strong> Set</p>";
    echo "<p><strong>Is Logged In:</strong> " . (is_logged_in($user) ? 'YES' : 'NO') . "</p>";
    
    if (isset($user_id_2)) {
        echo "<p><strong>User ID:</strong> " . $user_id_2 . "</p>";
    }
    if (isset($user_level_2)) {
        echo "<p><strong>User Level:</strong> " . $user_level_2 . "</p>";
    }
} else {
    echo "<p><strong>User Variable:</strong> Not Set</p>";
    echo "<p style='color: red;'><strong>Issue:</strong> User is not logged in or session is invalid</p>";
}

$base_url = $db->base_url();
echo "<p><strong>Base URL:</strong> " . $base_url . "</p>";

echo "<h2>AJAX Endpoints Test</h2>";

// List of endpoints to test
$endpoints = [
    'normal-serverside',
    'bulk-serverside', 
    'inactive-serverside',
    'trial-serverside',
    'reseller-serverside',
    'json-serverside',
    'notification-serverside'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Endpoint</th><th>File Exists</th><th>URL</th><th>Test Result</th></tr>";

foreach ($endpoints as $endpoint) {
    $file_path = "content/serverside/" . $endpoint . ".php";
    $file_exists = file_exists($file_path) ? "✓ Yes" : "✗ No";
    $full_url = $base_url . $endpoint;
    
    echo "<tr>";
    echo "<td>" . $endpoint . "</td>";
    echo "<td>" . $file_exists . "</td>";
    echo "<td><a href='" . $full_url . "' target='_blank'>" . $full_url . "</a></td>";
    
    if (file_exists($file_path)) {
        echo "<td><button onclick=\"testEndpoint('" . $endpoint . "')\">Test AJAX</button> <span id='result_" . $endpoint . "'></span></td>";
    } else {
        echo "<td style='color: red;'>File Missing</td>";
    }
    
    echo "</tr>";
}

echo "</table>";

echo "<h2>Manual Test</h2>";
echo "<p>Click the buttons above to test each endpoint with AJAX POST requests.</p>";
echo "<p>If you see authentication errors, you need to log in to the application first.</p>";

?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function testEndpoint(endpoint) {
    const resultSpan = document.getElementById('result_' + endpoint);
    resultSpan.innerHTML = 'Testing...';
    
    $.ajax({
        url: '<?php echo $base_url; ?>' + endpoint,
        type: 'POST',
        data: {
            draw: 1,
            start: 0,
            length: 10,
            search: {value: ''},
            order: [{column: 0, dir: 'asc'}],
            columns: [{data: 'user_id', searchable: true}]
        },
        dataType: 'json',
        success: function(data) {
            console.log('Success for ' + endpoint + ':', data);
            if (data && typeof data === 'object') {
                if (data.error) {
                    resultSpan.innerHTML = '<span style="color: red;">Error: ' + data.error + '</span>';
                } else if (data.data !== undefined) {
                    resultSpan.innerHTML = '<span style="color: green;">✓ Success (' + (data.recordsTotal || 0) + ' records)</span>';
                } else {
                    resultSpan.innerHTML = '<span style="color: orange;">⚠ Unexpected response format</span>';
                }
            } else {
                resultSpan.innerHTML = '<span style="color: orange;">⚠ Non-JSON response</span>';
            }
        },
        error: function(xhr, status, error) {
            console.log('Error for ' + endpoint + ':', xhr.responseText);
            let errorMsg = 'HTTP ' + xhr.status;
            if (xhr.responseText) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    errorMsg += ': ' + (response.message || response.error || 'Unknown error');
                } catch (e) {
                    errorMsg += ': ' + xhr.responseText.substring(0, 100);
                }
            }
            resultSpan.innerHTML = '<span style="color: red;">✗ ' + errorMsg + '</span>';
        }
    });
}

// Test if user is logged in by checking a simple endpoint
$(document).ready(function() {
    console.log('Base URL: <?php echo $base_url; ?>');
    console.log('User logged in: <?php echo isset($user) && is_logged_in($user) ? "true" : "false"; ?>');
});
</script>

<style>
table { margin: 20px 0; }
th, td { padding: 10px; text-align: left; }
button { padding: 5px 10px; cursor: pointer; }
</style>
