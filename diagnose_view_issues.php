<?php
// Diagnostic script for view-user and view-reseller issues
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>View Pages Diagnostic</h1>";
echo "<style>
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .code { background-color: #f5f5f5; padding: 10px; margin: 10px 0; font-family: monospace; }
</style>";

// Include functions
include './includes/functions.php';

echo "<h2>1. Authentication Status</h2>";
echo "<table>";
echo "<tr><th>Check</th><th>Status</th><th>Details</th></tr>";

if (isset($user) && is_logged_in($user)) {
    echo "<tr><td>User Authentication</td><td class='success'>✓</td><td>User is logged in</td></tr>";
    echo "<tr><td>User ID</td><td class='success'>✓</td><td>" . (isset($user_id_2) ? $user_id_2 : 'Not set') . "</td></tr>";
    echo "<tr><td>User Level</td><td class='success'>✓</td><td>" . (isset($user_level_2) ? $user_level_2 : 'Not set') . "</td></tr>";
    echo "<tr><td>User Name</td><td class='success'>✓</td><td>" . (isset($user_name_2) ? $user_name_2 : 'Not set') . "</td></tr>";
} else {
    echo "<tr><td>User Authentication</td><td class='error'>✗</td><td>User is not logged in</td></tr>";
    echo "<p><a href='" . $db->base_url() . "login'>Please login first to continue testing</a></p>";
    exit;
}

echo "</table>";

echo "<h2>2. Database Connection</h2>";
echo "<table>";
echo "<tr><th>Check</th><th>Status</th><th>Details</th></tr>";

try {
    $test_query = $db->sql_query("SELECT 1 as test");
    if ($test_query) {
        echo "<tr><td>Database Connection</td><td class='success'>✓</td><td>Connected</td></tr>";
        
        // Test users table
        $users_query = $db->sql_query("SELECT COUNT(*) as count FROM users");
        if ($users_query) {
            $users_row = $db->sql_fetchrow($users_query);
            echo "<tr><td>Users Table</td><td class='success'>✓</td><td>" . $users_row['count'] . " users found</td></tr>";
        }
        
        // Test specific user data
        if($user_id_2 == 1 || $user_level_2 == 'superadmin'){
            $normal_users_query = $db->sql_query("SELECT COUNT(*) as count FROM users WHERE user_level='normal'");
            $reseller_users_query = $db->sql_query("SELECT COUNT(*) as count FROM users WHERE user_level='reseller'");
        } else {
            $normal_users_query = $db->sql_query("SELECT COUNT(*) as count FROM users WHERE upline='$user_id_2' AND user_level='normal'");
            $reseller_users_query = $db->sql_query("SELECT COUNT(*) as count FROM users WHERE upline='$user_id_2' AND user_level='reseller'");
        }
        
        if ($normal_users_query) {
            $normal_row = $db->sql_fetchrow($normal_users_query);
            echo "<tr><td>Normal Users</td><td class='success'>✓</td><td>" . $normal_row['count'] . " normal users</td></tr>";
        }
        
        if ($reseller_users_query) {
            $reseller_row = $db->sql_fetchrow($reseller_users_query);
            echo "<tr><td>Reseller Users</td><td class='success'>✓</td><td>" . $reseller_row['count'] . " reseller users</td></tr>";
        }
        
    }
} catch (Exception $e) {
    echo "<tr><td>Database Connection</td><td class='error'>✗</td><td>Error: " . $e->getMessage() . "</td></tr>";
}

echo "</table>";

echo "<h2>3. File Accessibility</h2>";
echo "<table>";
echo "<tr><th>File</th><th>Exists</th><th>Readable</th><th>Path</th></tr>";

$files_to_check = [
    'content/view-user.php',
    'content/view-reseller.php',
    'content/serverside/normal-serverside.php',
    'content/serverside/reseller-serverside.php',
    'templates/view-user.tpl',
    'templates/view-reseller.tpl',
    'templates/js/page/viewuser_js.tpl',
    'templates/js/page/viewreseller_js.tpl'
];

foreach ($files_to_check as $file) {
    $exists = file_exists($file) ? "✓" : "✗";
    $readable = is_readable($file) ? "✓" : "✗";
    $exists_class = file_exists($file) ? "success" : "error";
    $readable_class = is_readable($file) ? "success" : "error";
    
    echo "<tr><td>$file</td><td class='$exists_class'>$exists</td><td class='$readable_class'>$readable</td><td>" . realpath($file) . "</td></tr>";
}

echo "</table>";

echo "<h2>4. AJAX Endpoints Test</h2>";
echo "<table>";
echo "<tr><th>Endpoint</th><th>URL</th><th>Status</th><th>Response</th></tr>";

$base_url = $db->base_url();
$endpoints = [
    'normal-serverside',
    'reseller-serverside'
];

foreach ($endpoints as $endpoint) {
    $url = $base_url . $endpoint;
    echo "<tr><td>$endpoint</td><td>$url</td><td id='status_$endpoint'>Testing...</td><td id='response_$endpoint'>-</td></tr>";
}

echo "</table>";

echo "<h2>5. Direct Endpoint Testing</h2>";
echo "<p>Testing serverside endpoints directly with POST data...</p>";

// Test normal-serverside endpoint
echo "<h3>Normal Users Endpoint Test</h3>";
$_POST = [
    'draw' => 1,
    'start' => 0,
    'length' => 10,
    'search' => ['value' => ''],
    'order' => [['column' => 0, 'dir' => 'asc']]
];
$_REQUEST = $_POST;

echo "<div class='code'>";
echo "Testing content/serverside/normal-serverside.php...<br>";
ob_start();
try {
    include 'content/serverside/normal-serverside.php';
    $output = ob_get_clean();
    echo "Response: " . htmlspecialchars(substr($output, 0, 500)) . (strlen($output) > 500 ? "..." : "");
} catch (Exception $e) {
    ob_end_clean();
    echo "Error: " . $e->getMessage();
}
echo "</div>";

// Test reseller-serverside endpoint
echo "<h3>Reseller Users Endpoint Test</h3>";
echo "<div class='code'>";
echo "Testing content/serverside/reseller-serverside.php...<br>";
ob_start();
try {
    include 'content/serverside/reseller-serverside.php';
    $output = ob_get_clean();
    echo "Response: " . htmlspecialchars(substr($output, 0, 500)) . (strlen($output) > 500 ? "..." : "");
} catch (Exception $e) {
    ob_end_clean();
    echo "Error: " . $e->getMessage();
}
echo "</div>";

echo "<h2>6. JavaScript Console Test</h2>";
echo "<p>Open browser console to see AJAX test results...</p>";

?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    console.log('Starting AJAX endpoint tests...');
    
    // Test endpoints with proper DataTables format
    var testData = {
        draw: 1,
        start: 0,
        length: 10,
        search: { value: '' },
        order: [{ column: 0, dir: 'asc' }]
    };
    
    var endpoints = ['normal-serverside', 'reseller-serverside'];
    var baseUrl = '<?php echo $base_url; ?>';
    
    endpoints.forEach(function(endpoint) {
        $.ajax({
            url: baseUrl + endpoint,
            type: 'POST',
            data: testData,
            dataType: 'json',
            success: function(response) {
                console.log('Success for ' + endpoint + ':', response);
                $('#status_' + endpoint.replace('-', '_')).html('<span class="success">✓ Success</span>');
                $('#response_' + endpoint.replace('-', '_')).html('Records: ' + (response.recordsTotal || 0));
            },
            error: function(xhr, status, error) {
                console.log('Error for ' + endpoint + ':', xhr.responseText);
                $('#status_' + endpoint.replace('-', '_')).html('<span class="error">✗ Error</span>');
                $('#response_' + endpoint.replace('-', '_')).html('HTTP ' + xhr.status + ': ' + error);
            }
        });
    });
});
</script>
