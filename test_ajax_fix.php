<?php
// Test AJAX Fix Script
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>AJAX Fix Test</h1>";
echo "<style>
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .test-button { background: #007cba; color: white; padding: 5px 10px; border: none; cursor: pointer; margin: 2px; }
    .test-all { background: #28a745; color: white; padding: 10px 20px; border: none; cursor: pointer; margin: 10px 0; }
</style>";

// Include the application functions
include './includes/functions.php';

echo "<h2>Current Status</h2>";
$base_url = $db->base_url();
echo "<p><strong>Base URL:</strong> " . $base_url . "</p>";

// Check if user is logged in
$logged_in = isset($user) && is_logged_in($user);
echo "<p><strong>Logged In:</strong> " . ($logged_in ? '<span class="success">YES</span>' : '<span class="error">NO</span>') . "</p>";

if ($logged_in) {
    echo "<p><strong>User ID:</strong> " . (isset($user_id_2) ? $user_id_2 : 'Not Set') . "</p>";
    echo "<p><strong>User Level:</strong> " . (isset($user_level_2) ? $user_level_2 : 'Not Set') . "</p>";
    
    $has_permission = ($user_id_2 == 1 || $user_level_2 == 'superadmin' || $user_level_2 == 'reseller' || $user_level_2 == 'developer');
    echo "<p><strong>Has Permissions:</strong> " . ($has_permission ? '<span class="success">YES</span>' : '<span class="error">NO</span>') . "</p>";
}

echo "<h2>AJAX Endpoints Test</h2>";

if (!$logged_in) {
    echo "<div style='background: #ffebee; padding: 15px; margin: 20px 0; border-left: 4px solid #f44336;'>";
    echo "<h3>⚠️ Please Login First</h3>";
    echo "<p>You need to be logged in to test the AJAX endpoints. <a href='" . $base_url . "login'>Click here to login</a></p>";
    echo "</div>";
} else {
    echo "<button class='test-all' onclick='testAllEndpoints()'>Test All Endpoints</button>";
    echo "<div id='overall-status'></div>";
    
    echo "<table>";
    echo "<tr><th>Endpoint</th><th>Type</th><th>Status</th><th>Action</th></tr>";
    
    $endpoints = [
        'get_userinfo' => 'GET',
        'get_useroption' => 'GET', 
        'get_active' => 'GET',
        'normal-serverside' => 'POST',
        'bulk-serverside' => 'POST',
        'inactive-serverside' => 'POST',
        'trial-serverside' => 'POST'
    ];
    
    foreach ($endpoints as $endpoint => $method) {
        echo "<tr>";
        echo "<td>" . $endpoint . "</td>";
        echo "<td>" . $method . "</td>";
        echo "<td><span id='status_" . str_replace('-', '_', $endpoint) . "'>Not Tested</span></td>";
        echo "<td><button class='test-button' onclick='testEndpoint(\"" . $endpoint . "\", \"" . $method . "\")'>Test</button></td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<h3>Test Results</h3>";
    echo "<div id='test-results'></div>";
}

?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
let testResults = {};

function testEndpoint(endpoint, method) {
    const statusId = 'status_' + endpoint.replace('-', '_');
    const statusSpan = document.getElementById(statusId);
    statusSpan.innerHTML = '<span style="color: blue;">Testing...</span>';
    
    let ajaxData = {};
    
    // Configure request data based on endpoint
    if (method === 'POST') {
        ajaxData = {
            draw: 1,
            start: 0,
            length: 10,
            search: {value: ''},
            order: [{column: 0, dir: 'asc'}],
            columns: [{data: 'user_id', searchable: true}]
        };
    } else if (endpoint.startsWith('get_')) {
        if (endpoint === 'get_userinfo' || endpoint === 'get_useroption') {
            // Use the current user's ID for testing
            ajaxData = {uid: <?php echo isset($user_id_2) ? $user_id_2 : 1; ?>};
        } else if (endpoint === 'get_active') {
            ajaxData = {type: 'normal'};
        }
    }
    
    $.ajax({
        url: '<?php echo $base_url; ?>' + endpoint,
        type: method,
        data: ajaxData,
        dataType: 'json',
        timeout: 15000,
        success: function(data) {
            console.log('Success for ' + endpoint + ':', data);
            testResults[endpoint] = {success: true, data: data};
            
            if (data && typeof data === 'object') {
                if (data.error) {
                    statusSpan.innerHTML = '<span class="error">Error: ' + data.error + '</span>';
                } else if (data.response !== undefined || data.data !== undefined || data.recordsTotal !== undefined) {
                    statusSpan.innerHTML = '<span class="success">✓ Success</span>';
                } else {
                    statusSpan.innerHTML = '<span class="warning">? Unexpected response</span>';
                }
            } else {
                statusSpan.innerHTML = '<span class="warning">? Non-JSON response</span>';
            }
            
            updateTestResults();
        },
        error: function(xhr, status, error) {
            console.error('Error for ' + endpoint + ':', xhr, status, error);
            testResults[endpoint] = {success: false, error: xhr.status + ': ' + error, response: xhr.responseText};
            
            let errorMsg = 'HTTP ' + xhr.status;
            if (xhr.responseText && xhr.responseText.length < 200) {
                errorMsg += ': ' + xhr.responseText.substring(0, 100);
            }
            statusSpan.innerHTML = '<span class="error">✗ ' + errorMsg + '</span>';
            
            updateTestResults();
        }
    });
}

function testAllEndpoints() {
    const endpoints = ['get_userinfo', 'get_useroption', 'get_active', 'normal-serverside', 'bulk-serverside', 'inactive-serverside', 'trial-serverside'];
    const methods = {'get_userinfo': 'GET', 'get_useroption': 'GET', 'get_active': 'GET', 'normal-serverside': 'POST', 'bulk-serverside': 'POST', 'inactive-serverside': 'POST', 'trial-serverside': 'POST'};
    
    testResults = {};
    document.getElementById('overall-status').innerHTML = '<p style="color: blue;">Testing all endpoints...</p>';
    
    endpoints.forEach((endpoint, index) => {
        setTimeout(() => {
            testEndpoint(endpoint, methods[endpoint]);
        }, index * 1000); // Stagger tests by 1 second
    });
    
    // Check overall status after all tests
    setTimeout(() => {
        checkOverallStatus();
    }, endpoints.length * 1000 + 2000);
}

function checkOverallStatus() {
    const totalTests = Object.keys(testResults).length;
    const successfulTests = Object.values(testResults).filter(result => result.success).length;
    
    const overallDiv = document.getElementById('overall-status');
    if (successfulTests === totalTests && totalTests > 0) {
        overallDiv.innerHTML = '<p class="success">🎉 All ' + totalTests + ' endpoints are working correctly!</p>';
    } else if (successfulTests > 0) {
        overallDiv.innerHTML = '<p class="warning">⚠️ ' + successfulTests + ' out of ' + totalTests + ' endpoints are working.</p>';
    } else {
        overallDiv.innerHTML = '<p class="error">❌ No endpoints are working. Check the detailed results below.</p>';
    }
}

function updateTestResults() {
    const resultsDiv = document.getElementById('test-results');
    let html = '';
    
    for (const [endpoint, result] of Object.entries(testResults)) {
        html += '<div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd;">';
        html += '<h4>' + endpoint + '</h4>';
        
        if (result.success) {
            html += '<p class="success">✓ Success</p>';
            if (result.data) {
                html += '<details><summary>Response Data</summary><pre>' + JSON.stringify(result.data, null, 2) + '</pre></details>';
            }
        } else {
            html += '<p class="error">✗ Failed: ' + result.error + '</p>';
            if (result.response) {
                html += '<details><summary>Error Response</summary><pre>' + result.response + '</pre></details>';
            }
        }
        
        html += '</div>';
    }
    
    resultsDiv.innerHTML = html;
}
</script>
