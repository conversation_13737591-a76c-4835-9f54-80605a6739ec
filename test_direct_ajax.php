<?php
// Direct AJAX endpoint test
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Direct AJAX Endpoint Test</h1>";

// Include functions to get session
include './includes/functions.php';

echo "<h2>Current Session Status</h2>";
if (isset($user) && is_logged_in($user)) {
    echo "<p style='color: green;'>✓ User is logged in</p>";
    echo "<p>User ID: " . (isset($user_id_2) ? $user_id_2 : 'Not set') . "</p>";
    echo "<p>User Level: " . (isset($user_level_2) ? $user_level_2 : 'Not set') . "</p>";
    echo "<p>User Name: " . (isset($user_name_2) ? $user_name_2 : 'Not set') . "</p>";
} else {
    echo "<p style='color: red;'>✗ User is not logged in</p>";
    echo "<p><a href='" . $db->base_url() . "login'>Please login first</a></p>";
}

echo "<h2>Test DataTables AJAX Request</h2>";

// Simulate the exact POST request that DataTables makes
$test_data = array(
    'draw' => 1,
    'start' => 0,
    'length' => 10,
    'search' => array('value' => ''),
    'order' => array(array('column' => 0, 'dir' => 'asc')),
    'columns' => array(
        array('data' => 'user_id', 'searchable' => true),
        array('data' => 'user_name', 'searchable' => true)
    )
);

echo "<p>Testing normal-serverside endpoint with DataTables parameters...</p>";

// Test the endpoint
$base_url = $db->base_url();
$endpoint_url = $base_url . 'normal-serverside';

echo "<p><strong>Endpoint URL:</strong> " . $endpoint_url . "</p>";

// Use cURL to test the endpoint
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $endpoint_url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($test_data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_COOKIEFILE, ''); // Enable cookie handling
curl_setopt($ch, CURLOPT_COOKIEJAR, ''); // Enable cookie handling

// Copy cookies from current session
if (isset($_COOKIE)) {
    $cookie_string = '';
    foreach ($_COOKIE as $name => $value) {
        $cookie_string .= $name . '=' . $value . '; ';
    }
    curl_setopt($ch, CURLOPT_COOKIE, rtrim($cookie_string, '; '));
}

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<h3>Response Details</h3>";
echo "<p><strong>HTTP Code:</strong> " . $http_code . "</p>";

if ($error) {
    echo "<p style='color: red;'><strong>cURL Error:</strong> " . $error . "</p>";
}

echo "<p><strong>Response Length:</strong> " . strlen($response) . " bytes</p>";

if ($response) {
    echo "<h3>Response Content</h3>";
    
    // Try to decode as JSON
    $json_data = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<p style='color: green;'>✓ Valid JSON response</p>";
        echo "<pre>" . json_encode($json_data, JSON_PRETTY_PRINT) . "</pre>";
        
        if (isset($json_data['data'])) {
            echo "<p><strong>Records returned:</strong> " . count($json_data['data']) . "</p>";
        }
        if (isset($json_data['recordsTotal'])) {
            echo "<p><strong>Total records:</strong> " . $json_data['recordsTotal'] . "</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠ Non-JSON response</p>";
        echo "<h4>Raw Response:</h4>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
    }
} else {
    echo "<p style='color: red;'>✗ No response received</p>";
}

echo "<h2>Manual Test Links</h2>";
echo "<p>Try these links manually:</p>";
echo "<ul>";
echo "<li><a href='" . $base_url . "normal-serverside' target='_blank'>normal-serverside</a></li>";
echo "<li><a href='" . $base_url . "view-user' target='_blank'>view-user page</a></li>";
echo "<li><a href='" . $base_url . "login' target='_blank'>login page</a></li>";
echo "</ul>";

// Check if the serverside file exists
$serverside_file = 'content/serverside/normal-serverside.php';
echo "<h2>File System Check</h2>";
echo "<p><strong>Serverside file exists:</strong> " . (file_exists($serverside_file) ? 'YES' : 'NO') . "</p>";

if (file_exists($serverside_file)) {
    echo "<p><strong>File permissions:</strong> " . substr(sprintf('%o', fileperms($serverside_file)), -4) . "</p>";
    echo "<p><strong>File size:</strong> " . filesize($serverside_file) . " bytes</p>";
}
?>
