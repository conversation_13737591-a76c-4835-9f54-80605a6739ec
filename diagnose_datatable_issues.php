<?php
// DataTables AJAX Issues Diagnostic Script
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>DataTables AJAX Issues Diagnostic</h1>";
echo "<style>
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; font-weight: bold; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .test-button { background: #007cba; color: white; padding: 5px 10px; border: none; cursor: pointer; margin: 2px; }
    .code-block { background: #f5f5f5; padding: 10px; border-left: 4px solid #007cba; margin: 10px 0; }
    .issue-box { background: #fff3cd; padding: 15px; margin: 10px 0; border-left: 4px solid #ffc107; }
</style>";

// Include the application functions
include './includes/functions.php';

echo "<h2>🔍 Environment Check</h2>";
$base_url = $db->base_url();
echo "<table>";
echo "<tr><th>Check</th><th>Status</th><th>Value</th></tr>";

// Check base URL
echo "<tr><td>Base URL</td><td class='" . (!empty($base_url) ? 'success' : 'error') . "'>" . 
     (!empty($base_url) ? '✓' : '✗') . "</td><td>" . $base_url . "</td></tr>";

// Check if user is logged in
$logged_in = isset($user) && is_logged_in($user);
echo "<tr><td>User Logged In</td><td class='" . ($logged_in ? 'success' : 'error') . "'>" . 
     ($logged_in ? '✓' : '✗') . "</td><td>" . ($logged_in ? 'Yes' : 'No') . "</td></tr>";

if ($logged_in) {
    echo "<tr><td>User ID</td><td class='success'>✓</td><td>" . (isset($user_id_2) ? $user_id_2 : 'Not Set') . "</td></tr>";
    echo "<tr><td>User Level</td><td class='success'>✓</td><td>" . (isset($user_level_2) ? $user_level_2 : 'Not Set') . "</td></tr>";
    
    $has_permission = ($user_id_2 == 1 || $user_level_2 == 'superadmin' || $user_level_2 == 'reseller' || $user_level_2 == 'developer');
    echo "<tr><td>Has Permissions</td><td class='" . ($has_permission ? 'success' : 'error') . "'>" . 
         ($has_permission ? '✓' : '✗') . "</td><td>" . ($has_permission ? 'Yes' : 'No') . "</td></tr>";
}

echo "</table>";

echo "<h2>📊 Database Check</h2>";
echo "<table>";
echo "<tr><th>Check</th><th>Status</th><th>Count</th></tr>";

try {
    // Check users table
    $users_query = $db->sql_query("SELECT COUNT(*) as count FROM users");
    if ($users_query) {
        $users_row = $db->sql_fetchrow($users_query);
        $total_users = $users_row['count'];
        echo "<tr><td>Total Users</td><td class='success'>✓</td><td>" . $total_users . "</td></tr>";
        
        // Check normal users
        $normal_query = $db->sql_query("SELECT COUNT(*) as count FROM users WHERE user_level='normal'");
        if ($normal_query) {
            $normal_row = $db->sql_fetchrow($normal_query);
            echo "<tr><td>Normal Users</td><td class='success'>✓</td><td>" . $normal_row['count'] . "</td></tr>";
        }
        
        // Check resellers
        $reseller_query = $db->sql_query("SELECT COUNT(*) as count FROM users WHERE user_level='reseller'");
        if ($reseller_query) {
            $reseller_row = $db->sql_fetchrow($reseller_query);
            echo "<tr><td>Resellers</td><td class='success'>✓</td><td>" . $reseller_row['count'] . "</td></tr>";
        }
        
        // Check bulk users
        $bulk_query = $db->sql_query("SELECT COUNT(*) as count FROM users WHERE user_level='bulk'");
        if ($bulk_query) {
            $bulk_row = $db->sql_fetchrow($bulk_query);
            echo "<tr><td>Bulk Users</td><td class='success'>✓</td><td>" . $bulk_row['count'] . "</td></tr>";
        }
        
    } else {
        echo "<tr><td>Users Table</td><td class='error'>✗</td><td>Query Failed</td></tr>";
    }
} catch (Exception $e) {
    echo "<tr><td>Database Error</td><td class='error'>✗</td><td>" . $e->getMessage() . "</td></tr>";
}

echo "</table>";

echo "<h2>🔧 Serverside Files Check</h2>";
$serverside_files = [
    'normal-serverside' => 'content/serverside/normal-serverside.php',
    'reseller-serverside' => 'content/serverside/reseller-serverside.php',
    'bulk-serverside' => 'content/serverside/bulk-serverside.php',
    'inactive-serverside' => 'content/serverside/inactive-serverside.php',
    'trial-serverside' => 'content/serverside/trial-serverside.php'
];

echo "<table>";
echo "<tr><th>Endpoint</th><th>File Exists</th><th>Has Include</th><th>Test</th></tr>";

foreach ($serverside_files as $endpoint => $file_path) {
    $file_exists = file_exists($file_path);
    $has_include = false;
    
    if ($file_exists) {
        $file_content = file_get_contents($file_path);
        $has_include = strpos($file_content, 'require_once') !== false || strpos($file_content, 'include') !== false;
    }
    
    echo "<tr>";
    echo "<td>" . $endpoint . "</td>";
    echo "<td class='" . ($file_exists ? 'success' : 'error') . "'>" . ($file_exists ? '✓' : '✗') . "</td>";
    echo "<td class='" . ($has_include ? 'success' : 'warning') . "'>" . ($has_include ? '✓' : '?') . "</td>";
    echo "<td><button class='test-button' onclick='testDataTableEndpoint(\"" . $endpoint . "\")'>Test</button> " .
         "<span id='result_" . str_replace('-', '_', $endpoint) . "'></span></td>";
    echo "</tr>";
}

echo "</table>";

if (!$logged_in) {
    echo "<div class='issue-box'>";
    echo "<h3>⚠️ Authentication Required</h3>";
    echo "<p>You need to be logged in to test the DataTables endpoints. Please <a href='" . $base_url . "login'>login</a> first.</p>";
    echo "</div>";
} else {
    echo "<h2>🧪 Live DataTables Test</h2>";
    echo "<p>Click the buttons above to test each DataTables endpoint with actual POST data.</p>";
    
    echo "<h3>Manual Test URLs</h3>";
    echo "<ul>";
    foreach ($serverside_files as $endpoint => $file_path) {
        $test_url = $base_url . $endpoint;
        echo "<li><a href='" . $test_url . "' target='_blank'>" . $endpoint . "</a></li>";
    }
    echo "</ul>";
    
    echo "<h3>Test Results</h3>";
    echo "<div id='test-results'></div>";
}

echo "<h2>🔍 Troubleshooting Steps</h2>";
echo "<div class='code-block'>";
echo "<h4>If DataTables are not loading:</h4>";
echo "<ol>";
echo "<li><strong>Check Browser Console:</strong> Open F12 → Console tab for JavaScript errors</li>";
echo "<li><strong>Check Network Tab:</strong> Open F12 → Network tab, reload page, look for failed requests</li>";
echo "<li><strong>Verify Permissions:</strong> Ensure you're logged in as superadmin/reseller</li>";
echo "<li><strong>Test Direct URLs:</strong> Click the manual test URLs above</li>";
echo "<li><strong>Check Server Logs:</strong> Look for PHP errors in XAMPP logs</li>";
echo "</ol>";
echo "</div>";

?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function testDataTableEndpoint(endpoint) {
    const resultId = 'result_' + endpoint.replace('-', '_');
    const resultSpan = document.getElementById(resultId);
    resultSpan.innerHTML = '<span style="color: blue;">Testing...</span>';
    
    // DataTables POST data format
    const postData = {
        draw: 1,
        start: 0,
        length: 10,
        search: {
            value: '',
            regex: false
        },
        order: [
            {
                column: 0,
                dir: 'asc'
            }
        ],
        columns: [
            {
                data: 'user_id',
                name: '',
                searchable: true,
                orderable: true,
                search: {
                    value: '',
                    regex: false
                }
            }
        ]
    };
    
    $.ajax({
        url: '<?php echo $base_url; ?>' + endpoint,
        type: 'POST',
        data: postData,
        dataType: 'json',
        timeout: 15000,
        success: function(data) {
            console.log('Success for ' + endpoint + ':', data);
            
            if (data && typeof data === 'object') {
                if (data.error) {
                    resultSpan.innerHTML = '<span style="color: red;">Error: ' + data.error + '</span>';
                } else if (data.data !== undefined && data.recordsTotal !== undefined) {
                    resultSpan.innerHTML = '<span style="color: green;">✓ Success (' + data.recordsTotal + ' records)</span>';
                    updateTestResults(endpoint, data);
                } else {
                    resultSpan.innerHTML = '<span style="color: orange;">? Unexpected response format</span>';
                }
            } else {
                resultSpan.innerHTML = '<span style="color: orange;">? Non-JSON response</span>';
            }
        },
        error: function(xhr, status, error) {
            console.error('Error for ' + endpoint + ':', xhr, status, error);
            
            let errorMsg = 'HTTP ' + xhr.status;
            if (xhr.responseText) {
                if (xhr.responseText.length < 200) {
                    errorMsg += ': ' + xhr.responseText;
                } else {
                    errorMsg += ' (See console for full response)';
                }
            }
            resultSpan.innerHTML = '<span style="color: red;">✗ ' + errorMsg + '</span>';
            
            // Log full response for debugging
            console.log('Full error response for ' + endpoint + ':', xhr.responseText);
        }
    });
}

function updateTestResults(endpoint, data) {
    const resultsDiv = document.getElementById('test-results');
    let html = resultsDiv.innerHTML;
    
    html += '<div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd;">';
    html += '<h4>' + endpoint + ' Results</h4>';
    html += '<p><strong>Records Total:</strong> ' + data.recordsTotal + '</p>';
    html += '<p><strong>Records Filtered:</strong> ' + data.recordsFiltered + '</p>';
    html += '<p><strong>Data Rows:</strong> ' + (data.data ? data.data.length : 0) + '</p>';
    
    if (data.data && data.data.length > 0) {
        html += '<details><summary>Sample Data (First Row)</summary>';
        html += '<pre>' + JSON.stringify(data.data[0], null, 2) + '</pre>';
        html += '</details>';
    }
    
    html += '</div>';
    resultsDiv.innerHTML = html;
}
</script>
