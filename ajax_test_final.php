<?php
// Final AJAX Test Script
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Final AJAX Test</h1>";
echo "<style>
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .test-result { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
</style>";

// Include functions
include './includes/functions.php';

echo "<h2>Authentication Check</h2>";
if (!isset($user) || !is_logged_in($user)) {
    echo "<p class='error'>Please login first: <a href='" . $db->base_url() . "login'>Login</a></p>";
    exit;
}

echo "<p class='success'>✓ User is authenticated</p>";

$base_url = $db->base_url();
?>

<div id="test-results"></div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    var testResults = $('#test-results');
    var baseUrl = '<?php echo $base_url; ?>';
    
    var testData = {
        draw: 1,
        start: 0,
        length: 10,
        search: { value: '' },
        order: [{ column: 0, dir: 'asc' }]
    };
    
    var endpoints = [
        { name: 'Normal Users', url: 'normal-serverside' },
        { name: 'Reseller Users', url: 'reseller-serverside' }
    ];
    
    function testEndpoint(endpoint, index) {
        var resultDiv = $('<div class="test-result"></div>');
        resultDiv.html('<h3>' + endpoint.name + '</h3><p>Testing...</p>');
        testResults.append(resultDiv);
        
        $.ajax({
            url: baseUrl + endpoint.url,
            type: 'POST',
            data: testData,
            dataType: 'json',
            timeout: 10000,
            success: function(response) {
                var html = '<h3 class="success">✓ ' + endpoint.name + ' - SUCCESS</h3>';
                html += '<p><strong>Records Total:</strong> ' + (response.recordsTotal || 0) + '</p>';
                html += '<p><strong>Data Rows:</strong> ' + (response.data ? response.data.length : 0) + '</p>';
                resultDiv.html(html);
                
                if (index < endpoints.length - 1) {
                    setTimeout(function() {
                        testEndpoint(endpoints[index + 1], index + 1);
                    }, 1000);
                }
            },
            error: function(xhr, status, error) {
                var html = '<h3 class="error">✗ ' + endpoint.name + ' - ERROR</h3>';
                html += '<p><strong>HTTP Status:</strong> ' + xhr.status + '</p>';
                html += '<p><strong>Error:</strong> ' + error + '</p>';
                resultDiv.html(html);
                
                if (index < endpoints.length - 1) {
                    setTimeout(function() {
                        testEndpoint(endpoints[index + 1], index + 1);
                    }, 1000);
                }
            }
        });
    }
    
    testEndpoint(endpoints[0], 0);
});
</script>

<h2>Manual Test Links</h2>
<ul>
    <li><a href="<?php echo $base_url; ?>view-user" target="_blank">View Users Page</a></li>
    <li><a href="<?php echo $base_url; ?>view-reseller" target="_blank">View Resellers Page</a></li>
</ul>
