<?php
// Test session and authentication
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Session Test</h1>";

// Include the functions
include './includes/functions.php';

echo "<h2>Cookie Information</h2>";
echo "<p><strong>User Cookie Exists:</strong> " . (isset($_COOKIE['user']) ? 'YES' : 'NO') . "</p>";

if (isset($_COOKIE['user'])) {
    echo "<p><strong>Cookie Value:</strong> " . substr($_COOKIE['user'], 0, 50) . "...</p>";
}

echo "<h2>Session Variables</h2>";
if (isset($user)) {
    echo "<p><strong>User Variable:</strong> Set</p>";
    echo "<p><strong>Is Logged In:</strong> " . (is_logged_in($user) ? 'YES' : 'NO') . "</p>";
} else {
    echo "<p><strong>User Variable:</strong> Not Set</p>";
}

if (isset($user_id_2)) {
    echo "<p><strong>User ID:</strong> " . $user_id_2 . "</p>";
}

if (isset($user_level_2)) {
    echo "<p><strong>User Level:</strong> " . $user_level_2 . "</p>";
}

if (isset($user_name_2)) {
    echo "<p><strong>User Name:</strong> " . $user_name_2 . "</p>";
}

echo "<h2>Database Connection</h2>";
if (isset($db)) {
    echo "<p><strong>Database Object:</strong> Available</p>";
    echo "<p><strong>Base URL:</strong> " . $db->base_url() . "</p>";
} else {
    echo "<p><strong>Database Object:</strong> Not Available</p>";
}

echo "<h2>Test AJAX Endpoint Access</h2>";
echo "<p>Testing if you can access a serverside endpoint...</p>";

// Test if user has permission to access normal-serverside
if (isset($user_id_2) && isset($user_level_2)) {
    if ($user_id_2 == 1 || $user_level_2 == 'superadmin' || $user_level_2 == 'reseller') {
        echo "<p style='color: green;'><strong>✓ Permission Check:</strong> You have permission to access user data endpoints</p>";
    } else {
        echo "<p style='color: red;'><strong>✗ Permission Check:</strong> You don't have permission to access user data endpoints</p>";
        echo "<p>Required: user_id = 1 OR user_level = 'superadmin' OR user_level = 'reseller'</p>";
        echo "<p>Your level: " . (isset($user_level_2) ? $user_level_2 : 'undefined') . "</p>";
    }
} else {
    echo "<p style='color: red;'><strong>✗ Permission Check:</strong> User ID or level not set</p>";
}

echo "<h2>Manual Login Test</h2>";
echo "<p>If you're not logged in, please <a href='" . $db->base_url() . "login'>click here to login</a></p>";

echo "<h2>Direct Endpoint Test</h2>";
echo "<p>Try accessing the endpoint directly:</p>";
echo "<p><a href='" . $db->base_url() . "normal-serverside' target='_blank'>" . $db->base_url() . "normal-serverside</a></p>";

// Check if error log has new entries
if (file_exists('error_log') && filesize('error_log') > 0) {
    echo "<h2>Recent Errors</h2>";
    echo "<pre>" . htmlspecialchars(file_get_contents('error_log')) . "</pre>";
} else {
    echo "<h2>No Recent Errors</h2>";
    echo "<p>Error log is empty or doesn't exist.</p>";
}
?>
