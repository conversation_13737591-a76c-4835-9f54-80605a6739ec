<?php
/* Smarty version 3.1.29, created on 2025-07-14 00:17:02
  from "C:\xampp\htdocs\RajaGenWeb\templates\js\page\login_js.tpl" */

if ($_smarty_tpl->smarty->ext->_validateCompiled->decodeProperties($_smarty_tpl, array (
  'has_nocache_code' => false,
  'version' => '3.1.29',
  'unifunc' => 'content_6874224eaa8533_65483090',
  'file_dependency' => 
  array (
    '5396e99830d9d6672ac0b0a3900a29e651ece0b8' => 
    array (
      0 => 'C:\\xampp\\htdocs\\RajaGenWeb\\templates\\js\\page\\login_js.tpl',
      1 => 1741775697,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_6874224eaa8533_65483090 ($_smarty_tpl) {
echo '<script'; ?>
>
function normalMessage(type, title, message) {
    $(".errors").html('<div class="alert alert-' + type + ' alert-has-icon"><div class="alert-icon"><i class="far fa-lightbulb"></i></div><div class="alert-body"><button class="close" data-dismiss="alert"><span>&times;</span></button><div class="alert-title">' + title + '</div>' + message + '</div></div>').slideDown();
}

$('document').ready(function()
{
    var $form = $('.authenticate');
	$form.ajaxForm({
		type: "POST",
		url: "<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
serverside/forms/login.php",
		data: $form.serialize(),
		dataType: "JSON",
		cache: false,
		beforeSend: function() {
			$(".btn-submit").addClass("btn-progress");
		},
		success: function(data){
			if(data.response == 1){
			    $('.btn-submit').addClass("disabled");
    			Swal.fire({
                        title: "Success",
                        icon: "success",
                        html: data.msg,
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        customClass: {
                            confirmButton: 'btn-primary'
                        },
                        timer: 3000,
                        timerProgressBar: true,
                        didOpen: () => {
                            Swal.showLoading();
                            const timer = Swal.getPopup().querySelector("b");
                            timerInterval = setInterval(() => {
                                timer.textContent = `${
                                    Swal.getTimerLeft()
                                }`;
                            }, 100);
                        },
                        willClose: () => {
                            clearInterval(timerInterval);
                        }
                    }).then((result) => {
                        if (result.dismiss === Swal.DismissReason.timer) {
                            location.reload()
                        }
                    });
    		}
    		if(data.response == 2){
                    			Swal.fire({
                                  title: "Failed",
                                  html: data.msg,
                                  icon: "error",
                                  allowOutsideClick: false,
                                  allowEscapeKey: false,
                                  didOpen: function () {
                                    Swal.getConfirmButton().blur()
                                  }
                                });
                    		}
                    		if(data.response == 3){
                    			Swal.fire({
                                  title: "Failed",
                                  html: data.errormsg,
                                  icon: "error",
                                  allowOutsideClick: false,
                                  allowEscapeKey: false
                                });
                    		}
    		if(data.response == 4){
    		    $('.btn-submit').prop("disabled", true);
    			Swal.fire({
                        title: "Success",
                        icon: "success",
                        html: data.msg,
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        customClass: {
                            confirmButton: 'btn-primary'
                        },
                        timer: 3000,
                        timerProgressBar: true,
                        didOpen: () => {
                            Swal.showLoading();
                            const timer = Swal.getPopup().querySelector("b");
                            timerInterval = setInterval(() => {
                                timer.textContent = `${
                                    Swal.getTimerLeft()
                                }`;
                            }, 100);
                        },
                        willClose: () => {
                            clearInterval(timerInterval);
                        }
                    }).then((result) => {
                        if (result.dismiss === Swal.DismissReason.timer) {
                            location.href = "confirmation?id=" + data.id
                        }
                    });
    		}
		},
		error: function(jqXHR, textStatus, errorThrown) {
			Swal.fire({
                        title: "Error",
                        icon: "error",
                        html: "Failed getting data from ajax.<br><b></b>",
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        customClass: {
                            confirmButton: 'btn-primary'
                        },
                        timer: 3000,
                        timerProgressBar: true,
                        didOpen: () => {
                            Swal.showLoading();
                            const timer = Swal.getPopup().querySelector("b");
                            timerInterval = setInterval(() => {
                                timer.textContent = `${
                                    Swal.getTimerLeft()
                                }`;
                            }, 100);
                        },
                        willClose: () => {
                            clearInterval(timerInterval);
                        }
                    }).then((result) => {
                        if (result.dismiss === Swal.DismissReason.timer) {
                            location.reload()
                        }
                    });
		},
		complete: function(){
			$(".btn-submit").removeClass("btn-progress");
		}
	});
	
	const passwordField = document.getElementById("user_pass");
    const togglePassword = document.querySelector(".password-toggle-icon i");

    togglePassword.addEventListener("click", function () {
      if (passwordField.type === "password") {
        passwordField.type = "text";
        togglePassword.classList.remove("fa-eye");
        togglePassword.classList.add("fa-eye-slash");
      } else {
        passwordField.type = "password";
        togglePassword.classList.remove("fa-eye-slash");
        togglePassword.classList.add("fa-eye");
      }
    });
});
<?php echo '</script'; ?>
><?php }
}
