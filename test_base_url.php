<?php
error_reporting(E_ALL);
ini_set('display_errors', '1');

echo "<h2>Base URL Debug Test</h2>";

// Test current base_url function
include './includes/functions.php';

echo "<h3>Current Configuration:</h3>";
echo "<p><strong>Base URL:</strong> " . $db->base_url() . "</p>";
echo "<p><strong>HTTP_HOST:</strong> " . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'NOT SET') . "</p>";
echo "<p><strong>SCRIPT_NAME:</strong> " . (isset($_SERVER['SCRIPT_NAME']) ? $_SERVER['SCRIPT_NAME'] : 'NOT SET') . "</p>";
echo "<p><strong>REQUEST_URI:</strong> " . (isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : 'NOT SET') . "</p>";

echo "<h3>AJAX Endpoint Tests:</h3>";

// Test the AJAX endpoints
$endpoints = [
    'get_userinfo',
    'get_useroption', 
    'get_active',
    'normal-serverside',
    'bulk-serverside',
    'inactive-serverside',
    'trial-serverside'
];

$base_url = $db->base_url();

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Endpoint</th><th>Full URL</th><th>File Exists</th><th>Status</th></tr>";

foreach ($endpoints as $endpoint) {
    $full_url = $base_url . $endpoint;
    
    // Check if file exists in content/serverside/
    $file_path = "content/serverside/" . $endpoint . ".php";
    $file_exists = file_exists($file_path) ? "YES" : "NO";
    
    // Check if file exists in serverside/data/ (for get_ endpoints)
    if (!file_exists($file_path) && strpos($endpoint, 'get_') === 0) {
        $file_path = "serverside/data/" . $endpoint . ".php";
        $file_exists = file_exists($file_path) ? "YES (in serverside/data/)" : "NO";
    }
    
    echo "<tr>";
    echo "<td>" . $endpoint . "</td>";
    echo "<td><a href='" . $full_url . "' target='_blank'>" . $full_url . "</a></td>";
    echo "<td>" . $file_exists . "</td>";
    echo "<td>" . ($file_exists !== "NO" ? "Available" : "Missing") . "</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>Manual Test Links:</h3>";
echo "<p>Click these links to test the endpoints manually:</p>";
echo "<ul>";
foreach ($endpoints as $endpoint) {
    $full_url = $base_url . $endpoint;
    echo "<li><a href='" . $full_url . "' target='_blank'>" . $endpoint . "</a></li>";
}
echo "</ul>";

echo "<h3>JavaScript Template Variable:</h3>";
echo "<p>In templates, {$base_url} should resolve to: <strong>" . $base_url . "</strong></p>";

?>
